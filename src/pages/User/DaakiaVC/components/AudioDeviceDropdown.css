.audio-device-dropdown {
  position: relative;
  display: inline-block;
}

.audio-device-dropdown-button {
  background: transparent;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 8px;
  border-radius: 4px;
  transition: background-color 0.2s;
  color: inherit;
}

.audio-device-dropdown-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.audio-device-dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ccc;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 200px;
  max-height: 200px;
  overflow-y: auto;
}

.audio-device-dropdown-menu .lk-media-device-select {
  padding: 8px;
}

.audio-device-dropdown-menu .lk-media-device-select button {
  width: 100%;
  text-align: left;
  padding: 8px 12px;
  border: none;
  background: transparent;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 2px;
  color: #333;
}

.audio-device-dropdown-menu .lk-media-device-select button:hover {
  background-color: #f5f5f5;
}

.audio-device-dropdown-menu .lk-media-device-select button.lk-active {
  background-color: #1890ff;
  color: white;
}
