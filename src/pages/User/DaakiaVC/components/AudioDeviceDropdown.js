import React, { useState, useEffect } from "react";
import { Dropdown } from "antd";
import { MediaDeviceSelect } from "@livekit/components-react";
import "./AudioDeviceDropdown.scss";

function AudioDeviceDropdown({
  kind = "audioinput",
  onActiveDeviceChange,
  initialSelection,
  icon = "🎤",
  className = ""
}) {
  const [selectedDevice, setSelectedDevice] = useState(initialSelection);

  useEffect(() => {
    setSelectedDevice(initialSelection);
  }, [initialSelection]);

  const handleDeviceChange = (deviceId) => {
    setSelectedDevice(deviceId);
    if (onActiveDeviceChange) {
      onActiveDeviceChange(deviceId);
    }
  };

  const dropdownContent = (
    <div className="audio-device-dropdown-menu">
      <MediaDeviceSelect
        kind={kind}
        onActiveDeviceChange={handleDeviceChange}
        initialSelection={selectedDevice}
      />
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="top"
      className={className}
    >
      <button
        className="audio-device-dropdown-button"
        type="button"
      >
        <span>{icon}</span>
        <span>▼</span>
      </button>
    </Dropdown>
  );
}

export default AudioDeviceDropdown;
