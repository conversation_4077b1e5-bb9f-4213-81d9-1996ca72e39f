import React, { useState } from "react";
import { MediaDeviceSelect } from "@livekit/components-react";
import "./AudioDeviceDropdown.css";

function AudioDeviceDropdown({
  kind = "audioinput",
  onActiveDeviceChange,
  initialSelection,
  icon = "🎤",
  className = ""
}) {
  const [showDropdown, setShowDropdown] = useState(false);

  const handleDeviceChange = (deviceId) => {
    if (onActiveDeviceChange) {
      onActiveDeviceChange(deviceId);
    }
    setShowDropdown(false); // Close dropdown after selection
  };

  return (
    <div className={`audio-device-dropdown ${className}`}>
      <button 
        className="audio-device-dropdown-button"
        onClick={() => setShowDropdown(!showDropdown)}
        type="button"
      >
        <span>{icon}</span>
        <span>{showDropdown ? '▲' : '▼'}</span>
      </button>
      
      {showDropdown && (
        <div className="audio-device-dropdown-menu">
          <MediaDeviceSelect
            kind={kind}
            onActiveDeviceChange={handleDeviceChange}
            initialSelection={initialSelection}
          />
        </div>
      )}
    </div>
  );
}

export default AudioDeviceDropdown;
