import React from "react";
import { Dropdown } from "antd";
import { MediaDeviceSelect } from "@livekit/components-react";
import "./AudioDeviceDropdown.css";

function AudioDeviceDropdown({
  kind = "audioinput",
  onActiveDeviceChange,
  initialSelection,
  icon = "🎤",
  className = ""
}) {
  const handleDeviceChange = (deviceId) => {
    if (onActiveDeviceChange) {
      onActiveDeviceChange(deviceId);
    }
  };

  const dropdownContent = (
    <div className="audio-device-dropdown-menu">
      <MediaDeviceSelect
        kind={kind}
        onActiveDeviceChange={handleDeviceChange}
        initialSelection={initialSelection}
      />
    </div>
  );

  return (
    <Dropdown
      dropdownRender={() => dropdownContent}
      trigger={['click']}
      placement="bottomRight"
      className={className}
    >
      <button
        className="audio-device-dropdown-button"
        type="button"
      >
        <span>{icon}</span>
        <span>▼</span>
      </button>
    </Dropdown>
  );
}

export default AudioDeviceDropdown;
