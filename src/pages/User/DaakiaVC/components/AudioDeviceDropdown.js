import React, { useState, useRef, useEffect } from "react";
import { MediaDeviceSelect } from "@livekit/components-react";
import "./AudioDeviceDropdown.css";

function AudioDeviceDropdown({
  kind = "audioinput",
  onActiveDeviceChange,
  initialSelection,
  icon = "🎤",
  className = ""
}) {
  const [showDropdown, setShowDropdown] = useState(false);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setShowDropdown(false);
      }
    };

    if (showDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showDropdown]);

  const handleDeviceChange = (deviceId) => {
    if (onActiveDeviceChange) {
      onActiveDeviceChange(deviceId);
    }
    setShowDropdown(false); // Close dropdown after selection
  };

  return (
    <div className={`audio-device-dropdown ${className}`} ref={dropdownRef}>
      <button 
        className="audio-device-dropdown-button"
        onClick={() => setShowDropdown(!showDropdown)}
        type="button"
      >
        <span>{icon}</span>
        <span>{showDropdown ? '▲' : '▼'}</span>
      </button>
      
      {showDropdown && (
        <div className="audio-device-dropdown-menu">
          <MediaDeviceSelect
            kind={kind}
            onActiveDeviceChange={handleDeviceChange}
            initialSelection={initialSelection}
          />
        </div>
      )}
    </div>
  );
}

export default AudioDeviceDropdown;
