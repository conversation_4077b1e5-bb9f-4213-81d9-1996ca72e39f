.audio-device-dropdown {
  position: relative;
  display: inline-block;

  &-button {
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 4px;
    padding: 8px;
    border-radius: 4px;
    transition: background-color 0.2s;
    color: inherit;

    &:hover {
      background-color: rgba(255, 255, 255, 0.1);
    }
  }

  &-menu {
    background: white;
    border: 1px solid #ccc;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    min-width: 200px;
    max-height: 200px;
    overflow-y: auto;
    padding: 8px;

    .lk-media-device-select {
      padding: 0;

      button {
        width: 100%;
        text-align: left;
        padding: 8px 12px;
        border: none;
        background: transparent;
        cursor: pointer;
        border-radius: 4px;
        margin-bottom: 2px;
        color: #333;
        transition: all 0.2s ease;

        &:hover {
          background-color: #f5f5f5;
        }

        &.lk-active,
        &[data-lk-active="true"] {
          background-color: #1890ff !important;
          color: white !important;
          font-weight: 500;
        }

        // Additional selector for selected state
        &[aria-pressed="true"] {
          background-color: #1890ff !important;
          color: white !important;
          font-weight: 500;
        }
      }
    }
  }
}
