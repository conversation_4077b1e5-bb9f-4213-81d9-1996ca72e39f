import React, { useEffect, useRef, useState } from 'react';
import { useLocalParticipant } from '@livekit/components-react';
import '../styles/RealTimeAudioWave.scss';

function RealTimeAudioWave({ className = '' }) {
  const { localParticipant } = useLocalParticipant();
  const [audioLevel, setAudioLevel] = useState(0);
  const [showInitialCircle, setShowInitialCircle] = useState(false);
  const [showDots, setShowDots] = useState(false);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [canAnalyzeAudio, setCanAnalyzeAudio] = useState(false);
  const analyserRef = useRef();
  const animationRef = useRef();
  const hasShownInitialAnimation = useRef(false);

  // Handle microphone state changes for initial animation
  useEffect(() => {
    const isActive = localParticipant?.isMicrophoneEnabled;

    if (isActive && !hasShownInitialAnimation.current) {
      // First time mic is turned on - show circle-to-dots animation
      setShowInitialCircle(true);
      setShowDots(false);
      setIsTransitioning(true);
      hasShownInitialAnimation.current = true;

      // Show dots during the explosion animation
      const dotsTimer = setTimeout(() => {
        setShowDots(true);
      }, 800); // Start dots when particles begin settling

      // Hide circle and end transition after animation completes
      const circleTimer = setTimeout(() => {
        setShowInitialCircle(false);
        setIsTransitioning(false);
      }, 2500); // Full explosion animation duration

      // Enable audio analysis ONLY after animation is completely done
      const audioTimer = setTimeout(() => {
        setCanAnalyzeAudio(true);
      }, 2800); // Wait extra 300ms after animation for smooth transition

      return () => {
        clearTimeout(dotsTimer);
        clearTimeout(circleTimer);
        clearTimeout(audioTimer);
      };
    } else if (isActive && hasShownInitialAnimation.current) {
      // Mic is on and we've already shown initial animation - just show dots
      setShowInitialCircle(false);
      setShowDots(true);
      setCanAnalyzeAudio(true); // Enable audio analysis immediately for subsequent uses
    } else {
      // Mic is off - disable audio analysis
      setCanAnalyzeAudio(false);
    }
    // When mic is off, we'll show a static circle (handled in render)
  }, [localParticipant?.isMicrophoneEnabled]);

  useEffect(() => {
    let audioContext;
    let sourceNode;

    const updateAudioLevel = () => {
      if (!analyserRef.current || !canAnalyzeAudio) {
        // Don't analyze audio during animation
        setAudioLevel(0);
        animationRef.current = requestAnimationFrame(updateAudioLevel);
        return;
      }

      const dataArray = new Uint8Array(analyserRef.current.frequencyBinCount);
      analyserRef.current.getByteFrequencyData(dataArray);

      // Use RMS (Root Mean Square) for better audio level detection
      const rms = Math.sqrt(dataArray.reduce((sum, value) => sum + value * value, 0) / dataArray.length);

      // Enhanced normalization with better sensitivity
      const normalizedLevel = Math.min(rms / 100, 1); // Adjusted divisor for better sensitivity

      // Apply exponential scaling for more dramatic visual response
      const enhancedLevel = normalizedLevel ** 0.7; // Power curve for better visual response

      setAudioLevel(enhancedLevel);
      animationRef.current = requestAnimationFrame(updateAudioLevel);
    };

    const setupAudioAnalysis = async () => {
      try {
        if (!localParticipant?.isMicrophoneEnabled) {
          setAudioLevel(0);
          return;
        }

        const micTrack = localParticipant.getTrackPublication('microphone')?.track;
        if (!micTrack?.mediaStream) {
          // Wait a bit and try again - track might be switching
          setTimeout(setupAudioAnalysis, 100);
          return;
        }

        audioContext = new (window.AudioContext || window.webkitAudioContext)();
        analyserRef.current = audioContext.createAnalyser();
        analyserRef.current.fftSize = 256;
        analyserRef.current.smoothingTimeConstant = 0.8;

        sourceNode = audioContext.createMediaStreamSource(micTrack.mediaStream);
        sourceNode.connect(analyserRef.current);

        updateAudioLevel();
      } catch (error) {
        console.error('Error setting up audio analysis:', error);
        setAudioLevel(0);
      }
    };

    setupAudioAnalysis();

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      if (audioContext && audioContext.state !== 'closed') {
        audioContext.close();
      }
      if (sourceNode) {
        sourceNode.disconnect();
      }
    };
  }, [localParticipant?.isMicrophoneEnabled, canAnalyzeAudio, localParticipant?.getTrackPublication('microphone')?.track?.mediaStream]);

  const isActive = localParticipant?.isMicrophoneEnabled;
  const isSpeaking = audioLevel > 0.05; // Lower threshold for better sensitivity

  // Enhanced scaling with more dramatic effects based on audio level
  // Base scale starts smaller, max scale is much larger
  const baseScale = 0.8; // Smaller base size
  const maxScale = 2.5; // Much larger max scale

  // Different scaling patterns for each dot to create wave effect
  const dot1Scale = baseScale + (audioLevel * maxScale * 0.6); // Left dot - moderate response
  const dot2Scale = baseScale + (audioLevel * maxScale * 1.0); // Middle dot - full response
  const dot3Scale = baseScale + (audioLevel * maxScale * 0.6); // Right dot - moderate response

  // Add some variation to make it more organic
  const variation1 = Math.sin(Date.now() * 0.01) * audioLevel * 0.1;
  const variation2 = Math.cos(Date.now() * 0.015) * audioLevel * 0.1;
  const variation3 = Math.sin(Date.now() * 0.008) * audioLevel * 0.1;

  const finalDot1Scale = Math.max(0.3, dot1Scale + variation1);
  const finalDot2Scale = Math.max(0.3, dot2Scale + variation2);
  const finalDot3Scale = Math.max(0.3, dot3Scale + variation3);

  return (
    <div
      className={`audio-dots ${className} ${isActive ? 'active' : 'inactive'} ${isSpeaking ? 'speaking' : ''} ${isTransitioning ? 'transitioning' : ''}`}
      style={{ '--audio-level': audioLevel }}
    >
      {/* Show initial circle animation when mic first turns on */}
      {showInitialCircle && (
        <div className="initial-circle" />
      )}

      {/* Show third particle during transition */}
      {isTransitioning && (
        <div className="third-particle" />
      )}

      {/* Show dots when mic is on and animation is complete */}
      {showDots && (
        <>
          <div
            className="dot"
            style={{ '--dot-scale': finalDot1Scale }}
          />
          <div
            className="dot"
            style={{ '--dot-scale': finalDot2Scale }}
          />
          <div
            className="dot"
            style={{ '--dot-scale': finalDot3Scale }}
          />
        </>
      )}

      {/* Show static circle when mic is off */}
      {!isActive && !showInitialCircle && !showDots && (
        <div className="static-circle" />
      )}
    </div>
  );
}

export default RealTimeAudioWave;
